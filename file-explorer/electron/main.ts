import { app, BrowserWindow, shell, ipc<PERSON>ain, dialog } from 'electron';
import path from 'path';
import url from 'url';
import fs from 'fs';
import crypto from 'crypto'; // ✅ Task 104: Import crypto for UUID generation
import isDev from 'electron-is-dev';
import { BoardStateService } from './services/board-state-service'; // New Import
import { AgentStateService } from './services/agent-state-service';
import { LLMService } from './services/llm-service';
import { MCPService } from './services/mcp-service';
import { spawn, IPty } from 'node-pty'; // ✅ Task 104: Import spawn and IPty type
import os from 'os';
// ✅ Task 101 Step 2: Import TerminalSessionManager
import { terminalSessionManager } from './services/TerminalSessionManager';

// Create a safer console.log that doesn't throw when streams are closed
const safeConsole = {
  log: (...args: any[]) => {
    try {
      console.log(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  },
  error: (...args: any[]) => {
    try {
      console.error(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  }
};



let mainWindow: BrowserWindow | null;

let kanbanWindow: BrowserWindow | null = null;
let agentSystemWindow: BrowserWindow | null = null;
let editorWindow: BrowserWindow | null = null;
let explorerWindow: BrowserWindow | null = null;
let chatWindow: BrowserWindow | null = null;
let timelineWindow: BrowserWindow | null = null;

let boardStateService: BoardStateService | null = null; // New: Declare BoardStateService instance
let agentStateService: AgentStateService | null = null; // New: Declare AgentStateService instance
let llmService: LLMService | null = null; // New: Declare LLMService instance
let mcpService: MCPService | null = null; // New: Declare MCPService instance

// Terminal management
const terminals: Record<string, IPty> = {};



function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false, // Best practice for security
      contextIsolation: true, // Best practice for security
      preload: path.join(__dirname, 'preload.js'), // Enable preload script for IPC
      devTools: true, // Always enable DevTools for debugging
      webSecurity: true, // Enable web security
    },
    icon: path.join(__dirname, '../public/placeholder-logo.svg') // Adjust path if needed
  });

  // Set Content Security Policy - More secure configuration
  mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          "default-src 'self'; " +
          "script-src 'self' 'unsafe-eval' 'unsafe-inline' blob: data: https://cdn.jsdelivr.net; " +
          "style-src 'self' 'unsafe-inline' data: https://cdn.jsdelivr.net; " +
          "img-src 'self' data: blob: https:; " +
          "font-src 'self' data: blob: https://cdn.jsdelivr.net; " +
          "connect-src 'self' ws: wss: http: https:; " +
          "worker-src 'self' blob: data: https://cdn.jsdelivr.net; " +
          "child-src 'self' blob: data:; " +
          "frame-src 'self' blob: data:; " +
          "media-src 'self' blob: data:;"
        ]
      }
    });
  });

  // Set a default title
  mainWindow.setTitle('CodeFusion - Modern Code Editor');

  // Determine the correct path to load
  const appPath = app.getAppPath();
  safeConsole.log('App path:', appPath);

  if (isDev || process.argv.includes('--dev')) {
    // In development, load from the Next.js dev server
    safeConsole.log('Loading from dev server');
    mainWindow.loadURL('http://localhost:4444');
    mainWindow.webContents.openDevTools();
  } else {
    // In production, load the statically exported Next.js app
    try {
      // Use a direct path to the index.html file
      const indexFile = path.join(__dirname, '../out/index.html');
      safeConsole.log('Checking for index.html at:', indexFile);

      if (fs.existsSync(indexFile)) {
        safeConsole.log('Found index.html at:', indexFile);
        const indexPath = url.format({
          pathname: indexFile,
          protocol: 'file:',
          slashes: true,
        });

        safeConsole.log('Loading from:', indexPath);
        mainWindow.loadFile(indexFile);
      } else {
        // Try alternative paths
        const possiblePaths = [
          path.join(process.cwd(), 'out/index.html'),
          path.join(app.getAppPath(), 'out/index.html')
        ];

        safeConsole.log('Checking alternative paths:');
        possiblePaths.forEach(p => safeConsole.log(' - ' + p));

        let found = false;
        for (const altPath of possiblePaths) {
          if (fs.existsSync(altPath)) {
            safeConsole.log('Found index.html at:', altPath);
            mainWindow.loadFile(altPath);
            found = true;
            break;
          }
        }

        if (!found) {
          throw new Error('Could not find index.html in any of the expected locations');
        }
      }

      // Only open DevTools in development or when explicitly requested
      if (process.argv.includes('--devtools')) {
        mainWindow.webContents.openDevTools();
      }
    } catch (error) {
      safeConsole.error('Error loading index.html:', error);

      // Show error in window
      mainWindow.loadURL(`data:text/html,<html><body><h1>Error</h1><p>${error}</p></body></html>`);

      // Always open DevTools when there's an error
      mainWindow.webContents.openDevTools();
    }
  }

  // Open external links in the default browser
  // New: Register main window with BoardStateService and AgentStateService
  if (boardStateService && mainWindow) {
    boardStateService.registerWindow(mainWindow);
  }
  if (agentStateService && mainWindow) {
    agentStateService.registerWindow(mainWindow);
  }

  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// Function to create any generic window and register with BoardStateService
function createGenericWindow(
  existingWindowVar: BrowserWindow | null,
  options: Electron.BrowserWindowConstructorOptions,
  loadUrl: string,
  onClosedCallback: () => void
): BrowserWindow {
  if (existingWindowVar) {
    existingWindowVar.focus();
    return existingWindowVar;
  }

  const newWindow = new BrowserWindow({
    ...options, // Spread default/passed options
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
      ...options.webPreferences, // Allow override
    },
    icon: options.icon || path.join(__dirname, '../public/placeholder-logo.svg')
  });

  newWindow.loadURL(loadUrl);

  newWindow.once('ready-to-show', () => {
    newWindow?.show();
    if (process.argv.includes('--devtools')) {
      newWindow?.webContents.openDevTools();
    }
  });

  newWindow.on('closed', onClosedCallback);

  // New: Register the new window with BoardStateService and AgentStateService
  if (boardStateService) {
    boardStateService.registerWindow(newWindow);
  }
  if (agentStateService) {
    agentStateService.registerWindow(newWindow);
  }
  return newWindow;
}



function createKanbanWindow(boardId: string) {
  const kanbanUrl = isDev || process.argv.includes('--dev')
    ? `http://localhost:4444/kanban/${boardId}`
    : url.format({
        pathname: path.join(__dirname, `../out/kanban/${boardId}/index.html`),
        protocol: 'file:',
        slashes: true,
      });
  kanbanWindow = createGenericWindow(
    kanbanWindow,
    { width: 1200, height: 800, minWidth: 800, minHeight: 600, frame: true, show: false, title: 'Kanban Board - CodeFusion' },
    kanbanUrl,
    () => { kanbanWindow = null; }
  );
}

function createAgentSystemWindow() {
  if (agentSystemWindow) {
    agentSystemWindow.focus();
    return;
  }

  agentSystemWindow = new BrowserWindow({
    width: 1000,
    height: 700,
    minWidth: 600,
    minHeight: 400,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'Agent System - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  // Load the dedicated agent system route
  const agentUrl = isDev || process.argv.includes('--dev')
    ? 'http://localhost:4444/agent-system'
    : url.format({
        pathname: path.join(__dirname, '../out/agent-system/index.html'),
        protocol: 'file:',
        slashes: true,
      });

  agentSystemWindow.loadURL(agentUrl);

  agentSystemWindow.once('ready-to-show', () => {
    agentSystemWindow?.show();
    if (process.argv.includes('--devtools')) {
      agentSystemWindow?.webContents.openDevTools();
    }
  });

  agentSystemWindow.on('closed', () => {
    agentSystemWindow = null;
  });

  // Register agent system window with AgentStateService
  if (agentStateService) {
    agentStateService.registerWindow(agentSystemWindow);
  }
}

function createEditorWindow(filePath?: string) {
  if (editorWindow) {
    editorWindow.focus();
    return;
  }

  editorWindow = new BrowserWindow({
    width: 1000,
    height: 700,
    minWidth: 600,
    minHeight: 400,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'Editor - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  // Load the dedicated editor route
  const editorUrl = isDev || process.argv.includes('--dev')
    ? `http://localhost:4444/editor${filePath ? `?file=${encodeURIComponent(filePath)}` : ''}`
    : url.format({
        pathname: path.join(__dirname, '../out/editor/index.html'),
        protocol: 'file:',
        slashes: true,
      });

  editorWindow.loadURL(editorUrl);

  editorWindow.once('ready-to-show', () => {
    editorWindow?.show();
    if (process.argv.includes('--devtools')) {
      editorWindow?.webContents.openDevTools();
    }
  });

  editorWindow.on('closed', () => {
    editorWindow = null;
  });
}

function createExplorerWindow() {
  if (explorerWindow) {
    explorerWindow.focus();
    return;
  }

  explorerWindow = new BrowserWindow({
    width: 400,
    height: 600,
    minWidth: 300,
    minHeight: 400,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'Explorer - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  // Load the dedicated explorer route
  const explorerUrl = isDev || process.argv.includes('--dev')
    ? 'http://localhost:4444/explorer'
    : url.format({
        pathname: path.join(__dirname, '../out/explorer/index.html'),
        protocol: 'file:',
        slashes: true,
      });

  explorerWindow.loadURL(explorerUrl);

  explorerWindow.once('ready-to-show', () => {
    explorerWindow?.show();
    if (process.argv.includes('--devtools')) {
      explorerWindow?.webContents.openDevTools();
    }
  });

  explorerWindow.on('closed', () => {
    explorerWindow = null;
  });
}

function createChatWindow() {
  if (chatWindow) {
    chatWindow.focus();
    return;
  }

  chatWindow = new BrowserWindow({
    width: 400,
    height: 600,
    minWidth: 300,
    minHeight: 400,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'AI Chat - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  // Load the dedicated chat route
  const chatUrl = isDev || process.argv.includes('--dev')
    ? 'http://localhost:4444/chat'
    : url.format({
        pathname: path.join(__dirname, '../out/chat/index.html'),
        protocol: 'file:',
        slashes: true,
      });

  chatWindow.loadURL(chatUrl);

  chatWindow.once('ready-to-show', () => {
    chatWindow?.show();
    if (process.argv.includes('--devtools')) {
      chatWindow?.webContents.openDevTools();
    }
  });

  chatWindow.on('closed', () => {
    chatWindow = null;
  });
}

function createTimelineWindow() {
  if (timelineWindow) {
    timelineWindow.focus();
    return;
  }

  timelineWindow = new BrowserWindow({
    width: 1000,
    height: 700,
    minWidth: 800,
    minHeight: 500,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'Task Timeline Inspector - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  // Load the dedicated timeline route
  const timelineUrl = isDev || process.argv.includes('--dev')
    ? 'http://localhost:4444/timeline'
    : url.format({
        pathname: path.join(__dirname, '../out/timeline/index.html'),
        protocol: 'file:',
        slashes: true,
      });

  timelineWindow.loadURL(timelineUrl);

  timelineWindow.once('ready-to-show', () => {
    timelineWindow?.show();
    if (process.argv.includes('--devtools')) {
      timelineWindow?.webContents.openDevTools();
    }
  });

  timelineWindow.on('closed', () => {
    timelineWindow = null;
  });
}

let terminalWindow: BrowserWindow | null = null;

function createTerminalWindow() {
  if (terminalWindow) {
    terminalWindow.focus();
    return;
  }

  terminalWindow = new BrowserWindow({
    width: 1000,
    height: 400,
    minWidth: 600,
    minHeight: 200,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'Terminal - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  // Load the dedicated terminal route
  const terminalUrl = isDev || process.argv.includes('--dev')
    ? 'http://localhost:4444/terminal'
    : url.format({
        pathname: path.join(__dirname, '../out/terminal/index.html'),
        protocol: 'file:',
        slashes: true,
      });

  terminalWindow.loadURL(terminalUrl);

  terminalWindow.once('ready-to-show', () => {
    terminalWindow?.show();
    if (process.argv.includes('--devtools')) {
      terminalWindow?.webContents.openDevTools();
    }
  });

  terminalWindow.on('closed', () => {
    terminalWindow = null;
  });

  // Register terminal window with services
  if (boardStateService) {
    boardStateService.registerWindow(terminalWindow);
  }
  if (agentStateService) {
    agentStateService.registerWindow(terminalWindow);
  }
}

app.on('ready', () => {
  // New: Initialize BoardStateService, AgentStateService, LLMService, and MCPService
  if (!boardStateService) {
    boardStateService = new BoardStateService();
  }
  if (!agentStateService) {
    agentStateService = new AgentStateService();
  }
  if (!llmService) {
    llmService = new LLMService();
  }
  if (!mcpService) {
    mcpService = new MCPService();
  }

  createWindow();
  // Register IPC handlers after main window is ready
  ipcMain.on('open-kanban-window', (event, boardId: string) => {
    createKanbanWindow(boardId);
  });
  ipcMain.on('open-agent-system-window', () => {
    createAgentSystemWindow();
  });
  ipcMain.on('open-editor-window', (event, filePath?: string) => {
    createEditorWindow(filePath);
  });
  ipcMain.on('open-explorer-window', () => {
    createExplorerWindow();
  });
  ipcMain.on('open-chat-window', () => {
    createChatWindow();
  });
  ipcMain.on('open-timeline-window', () => {
    createTimelineWindow();
  });

  ipcMain.on('open-terminal-window', () => {
    createTerminalWindow();
  });

  // Register file sync IPC handlers for real-time synchronization
  ipcMain.on('file-sync-event', (event, syncEvent) => {
    // Broadcast file sync events to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('file-sync-event', syncEvent);
      }
    });
  });

  // ✅ Terminal synchronization IPC handlers
  ipcMain.on('terminal-state-update', (event, terminalState) => {
    // Broadcast terminal state updates to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('terminal-state-update', terminalState);
      }
    });
  });

  ipcMain.on('terminal-session-created', (event, session) => {
    // Broadcast session creation to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('terminal-session-created', session);
      }
    });
  });

  ipcMain.on('terminal-session-closed', (event, sessionId) => {
    // Broadcast session closure to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('terminal-session-closed', sessionId);
      }
    });
  });

  ipcMain.on('request-terminal-state', (event, requestData) => {
    // Forward terminal state request to all other windows
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('request-terminal-state', requestData);
      }
    });
  });

  // ✅ Timeline synchronization IPC handlers
  ipcMain.on('timeline-data-update', (event, timelineData) => {
    // Broadcast timeline data updates to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('timeline-data-update', timelineData);
      }
    });
  });

  ipcMain.on('timeline-task-update', (event, taskUpdate) => {
    // Broadcast individual task updates to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('timeline-task-update', taskUpdate);
      }
    });
  });

  ipcMain.on('timeline-agent-activity', (event, activityData) => {
    // Broadcast agent activity to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('timeline-agent-activity', activityData);
      }
    });
  });

  // ✅ Register timeline data sync IPC handlers for cross-window synchronization
  ipcMain.on('request-timeline-data', (event, requestEvent) => {
    // Broadcast timeline data requests to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('request-timeline-data', requestEvent);
      }
    });
  });

  ipcMain.on('timeline-data-response', (event, responseEvent) => {
    // Send timeline data response to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('timeline-data-response', responseEvent);
      }
    });
  });

  // Register editor state IPC handlers for cross-window synchronization
  ipcMain.on('editor-state-event', (event, editorStateEvent) => {
    // Broadcast editor state events to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('editor-state-event', editorStateEvent);
      }
    });
  });

  // Handle initial state requests from new windows
  ipcMain.on('request-initial-editor-state', (event, requestData) => {
    // Broadcast the request to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('request-initial-editor-state', requestData);
      }
    });
  });

  // ✅ Auto-Save IPC Handlers
  ipcMain.handle('save-agent-states', async (event, agentStates) => {
    try {
      if (agentStateService) {
        await agentStateService.saveAgentStates(agentStates);
        return { success: true };
      }
      return { success: false, error: 'Agent state service not available' };
    } catch (error) {
      safeConsole.error('Failed to save agent states:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });

  ipcMain.handle('save-board-state', async (event, boardState) => {
    try {
      if (boardStateService) {
        await boardStateService.saveBoardState(boardState);
        return { success: true };
      }
      return { success: false, error: 'Board state service not available' };
    } catch (error) {
      safeConsole.error('Failed to save board state:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });

  // ✅ Note: save-file handler is registered later in the file system operations section

  // Register editor action state IPC handlers for cross-window synchronization
  ipcMain.on('editor-action-event', (event, actionEvent) => {
    // Broadcast action events to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('editor-action-event', actionEvent);
      }
    });
  });

  // ✅ Register chat state IPC handlers for real-time synchronization
  ipcMain.on('chat-state-update', (event, chatState) => {
    // Broadcast chat state to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('chat-state-update', chatState);
      }
    });
  });

  ipcMain.on('chat-message-added', (event, message) => {
    // Broadcast new message to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('chat-message-added', message);
      }
    });
  });

  ipcMain.on('chat-processing-changed', (event, isProcessing, streamingMessageId) => {
    // Broadcast processing state to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('chat-processing-changed', isProcessing, streamingMessageId);
      }
    });
  });

  ipcMain.on('chat-message-updated', (event, messageId, updates) => {
    // Broadcast message update to all windows except the sender
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (window.webContents !== event.sender) {
        window.webContents.send('chat-message-updated', messageId, updates);
      }
    });
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow();
  }
  // Also reactivate floating windows if they were minimized/hidden by activate event
  if (kanbanWindow) {
    kanbanWindow.show();
  }
  if (agentSystemWindow) {
    agentSystemWindow.show();
  }
  if (editorWindow) {
    editorWindow.show();
  }
  if (explorerWindow) {
    explorerWindow.show();
  }
  if (chatWindow) {
    chatWindow.show();
  }
  if (timelineWindow) {
    timelineWindow.show();
  }
});

// IPC handlers for file system operations
ipcMain.handle('select-folder', async () => {
  const result = await dialog.showOpenDialog(mainWindow!, {
    properties: ['openDirectory'],
    title: 'Select Project Folder'
  });

  if (!result.canceled && result.filePaths.length > 0) {
    const folderPath = result.filePaths[0];
    return {
      success: true,
      path: folderPath,
      name: path.basename(folderPath)
    };
  }

  return { success: false };
});

ipcMain.handle('read-directory', async (event, dirPath: string) => {
  try {
    const items = fs.readdirSync(dirPath, { withFileTypes: true });
    const result = [];

    for (const item of items) {
      const itemPath = path.join(dirPath, item.name);
      const stats = fs.statSync(itemPath);

      if (item.isDirectory()) {
        result.push({
          id: Date.now() + Math.random(),
          name: item.name,
          type: 'folder',
          path: itemPath,
          expanded: false,
          files: []
        });
      } else {
        const ext = path.extname(item.name).slice(1);
        result.push({
          id: Date.now() + Math.random(),
          name: item.name,
          type: ext || 'file',
          path: itemPath,
          size: stats.size,
          modified: stats.mtime
        });
      }
    }

    return { success: true, items: result };
  } catch (error) {
    safeConsole.error('Error reading directory:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('read-file', async (event, filePath: string) => {
  try {
    const content = fs.readFileSync(filePath, 'utf-8');
    return { success: true, content };
  } catch (error) {
    safeConsole.error('Error reading file:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

// ✅ Task 78: Taskmaster Task Sync IPC Handlers
ipcMain.handle('taskmaster:update-task', async (event, taskId: string, updatedFields: any) => {
  try {
    safeConsole.log(`Updating Taskmaster task ${taskId}:`, updatedFields);

    // This is a placeholder implementation
    // In a real implementation, you would:
    // 1. Load the tasks.json file
    // 2. Find the task by ID
    // 3. Update the task with new fields
    // 4. Save the file back

    return { success: true, taskId, updatedFields };
  } catch (error) {
    safeConsole.error('Error updating Taskmaster task:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('taskmaster:backup-tasks', async (event, projectPath: string) => {
  try {
    const tasksFilePath = path.join(projectPath, '.taskmaster', 'tasks.json');
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(projectPath, '.taskmaster', `tasks.backup.${timestamp}.json`);

    if (fs.existsSync(tasksFilePath)) {
      fs.copyFileSync(tasksFilePath, backupPath);
      safeConsole.log(`Created Taskmaster backup: ${backupPath}`);
      return { success: true, backupPath };
    } else {
      return { success: false, error: 'Tasks file not found' };
    }
  } catch (error) {
    safeConsole.error('Error creating Taskmaster backup:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('taskmaster:validate-tasks', async (event, filePath: string) => {
  try {
    if (!fs.existsSync(filePath)) {
      return { success: false, error: 'File does not exist' };
    }

    const content = fs.readFileSync(filePath, 'utf-8');
    const data = JSON.parse(content);

    // Basic validation
    if (!data.tasks || !Array.isArray(data.tasks)) {
      return { success: false, error: 'Invalid tasks structure' };
    }

    return { success: true, taskCount: data.tasks.length };
  } catch (error) {
    safeConsole.error('Error validating Taskmaster tasks:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('taskmaster:sync-status', async (event, taskId: string, status: string, metadata: any) => {
  try {
    safeConsole.log(`Syncing task status for ${taskId}: ${status}`, metadata);

    // This is a placeholder implementation
    // In a real implementation, you would update the specific task's status

    return { success: true, taskId, status, metadata };
  } catch (error) {
    safeConsole.error('Error syncing task status:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('save-file', async (event, filePath: string, content: string) => {
  try {
    fs.writeFileSync(filePath, content, 'utf-8');

    // Broadcast save success to all windows
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      window.webContents.send('file-saved', {
        filePath,
        content,
        timestamp: Date.now()
      });
    });

    return { success: true };
  } catch (error) {
    safeConsole.error('Error saving file:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('create-file', async (event, filePath: string, content: string = '') => {
  try {
    // Check if file already exists
    if (fs.existsSync(filePath)) {
      return { success: false, error: 'File already exists' };
    }

    // Create directory if it doesn't exist
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(filePath, content, 'utf-8');
    return { success: true };
  } catch (error) {
    safeConsole.error('Error creating file:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('delete-file', async (event, filePath: string) => {
  try {
    const stats = fs.statSync(filePath);
    if (stats.isDirectory()) {
      fs.rmSync(filePath, { recursive: true, force: true });
    } else {
      fs.unlinkSync(filePath);
    }
    return { success: true };
  } catch (error) {
    safeConsole.error('Error deleting file:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

// ✅ Command execution handler for PRD parsing and other CLI operations
ipcMain.handle('execute-command', async (event, command: string, workingDirectory?: string) => {
  const { spawn } = require('child_process');

  try {
    const cwd = workingDirectory || process.cwd();
    safeConsole.log(`📁 Execute Command - Working Directory: ${cwd}`);
    safeConsole.log(`🔧 Execute Command - Command: ${command}`);
    safeConsole.log(`📋 Execute Command - Received workingDirectory param: ${workingDirectory || 'undefined'}`);

    return new Promise((resolve) => {

      // Parse command and arguments
      const parts = command.split(' ');
      const cmd = parts[0];
      const args = parts.slice(1);

      const childProcess = spawn(cmd, args, {
        cwd,
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true,
        env: process.env // Use default environment
      });

      let stdout = '';
      let stderr = '';

      childProcess.stdout?.on('data', (data: Buffer) => {
        stdout += data.toString();
      });

      childProcess.stderr?.on('data', (data: Buffer) => {
        stderr += data.toString();
      });

      childProcess.on('close', (code: number | null) => {
        const success = code === 0;
        const output = stdout || stderr;

        safeConsole.log(`Command completed with code ${code}`);
        if (stdout) safeConsole.log('STDOUT:', stdout);
        if (stderr) safeConsole.log('STDERR:', stderr);

        resolve({
          success,
          output,
          error: success ? null : stderr || `Command failed with exit code ${code}`,
          exitCode: code || -1
        });
      });

      childProcess.on('error', (error: Error) => {
        safeConsole.error('Command execution error:', error);
        resolve({
          success: false,
          output: '',
          error: error.message,
          exitCode: -1
        });
      });

      // Set timeout for long-running commands
      setTimeout(() => {
        if (!childProcess.killed) {
          childProcess.kill();
          resolve({
            success: false,
            output: stdout,
            error: 'Command timed out after 30 seconds',
            exitCode: -1
          });
        }
      }, 30000);
    });
  } catch (error) {
    safeConsole.error('Error executing command:', error);
    return {
      success: false,
      output: '',
      error: error instanceof Error ? error.message : 'Unknown error',
      exitCode: -1
    };
  }
});

// ✅ Command execution handler with environment variables for Taskmaster API keys
ipcMain.handle('execute-command-with-env', async (event, command: string, workingDirectory?: string, envVars?: Record<string, string>) => {
  const { spawn } = require('child_process');

  try {
    const cwd = workingDirectory || process.cwd();
    safeConsole.log(`📁 Execute Command with Env - Working Directory: ${cwd}`);
    safeConsole.log(`🔧 Execute Command with Env - Command: ${command}`);
    safeConsole.log(`🔑 Execute Command with Env - Environment Variables: ${Object.keys(envVars || {}).length} keys`);
    safeConsole.log(`📋 Execute Command with Env - Received workingDirectory param: ${workingDirectory || 'undefined'}`);

    return new Promise((resolve) => {
      // Parse command and arguments
      const parts = command.split(' ');
      const cmd = parts[0];
      const args = parts.slice(1);

      // ✅ Merge environment variables with process.env
      const mergedEnv = {
        ...process.env,
        ...envVars
      };

      // ✅ Security: Never log API keys
      const safeEnvKeys = Object.keys(envVars || {}).map(key =>
        key.includes('API_KEY') ? `${key}=***` : `${key}=${envVars![key]}`
      );
      safeConsole.log(`🔑 Environment variables: ${safeEnvKeys.join(', ')}`);

      const childProcess = spawn(cmd, args, {
        cwd,
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true,
        env: mergedEnv
      });

      let stdout = '';
      let stderr = '';

      childProcess.stdout?.on('data', (data: Buffer) => {
        stdout += data.toString();
      });

      childProcess.stderr?.on('data', (data: Buffer) => {
        stderr += data.toString();
      });

      childProcess.on('close', (code: number | null) => {
        const success = code === 0;
        const output = stdout || stderr;

        safeConsole.log(`Command with env completed with code ${code}`);
        if (stdout) safeConsole.log('STDOUT:', stdout);
        if (stderr) safeConsole.log('STDERR:', stderr);

        resolve({
          success,
          output,
          error: success ? null : stderr || `Command failed with exit code ${code}`,
          exitCode: code || -1
        });
      });

      childProcess.on('error', (error: Error) => {
        safeConsole.error('Command with env execution error:', error);
        resolve({
          success: false,
          output: '',
          error: error.message,
          exitCode: -1
        });
      });

      // Set timeout for long-running commands
      setTimeout(() => {
        if (!childProcess.killed) {
          childProcess.kill();
          resolve({
            success: false,
            output: stdout,
            error: 'Command timed out after 30 seconds',
            exitCode: -1
          });
        }
      }, 30000);
    });
  } catch (error) {
    safeConsole.error('Error executing command with env:', error);
    return {
      success: false,
      output: '',
      error: error instanceof Error ? error.message : 'Unknown error',
      exitCode: -1
    };
  }
});

// ✅ MCP Protocol IPC Handlers
ipcMain.handle('mcp:initializeConnection', async (event, serverId: string, config: any) => {
  if (!mcpService) {
    return {
      success: false,
      error: 'MCP service not initialized'
    };
  }

  try {
    const result = await mcpService.initializeConnection(serverId, {
      command: config.command,
      args: config.args || [],
      timeout: config.timeout || 30000,
      maxRetries: config.maxRetries || 3
    });

    return result;
  } catch (error) {
    safeConsole.error('MCP connection error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown MCP connection error'
    };
  }
});

ipcMain.handle('mcp:sendTask', async (event, serverId: string, request: any) => {
  if (!mcpService) {
    return {
      success: false,
      error: 'MCP service not initialized'
    };
  }

  try {
    const result = await mcpService.sendTask(serverId, request);
    return result;
  } catch (error) {
    safeConsole.error('MCP task error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown MCP task error'
    };
  }
});

ipcMain.handle('mcp:syncAgentState', async (event, serverId: string, agentId: string, state: any) => {
  if (!mcpService) {
    return {
      success: false,
      error: 'MCP service not initialized'
    };
  }

  try {
    const result = await mcpService.syncAgentState(serverId, agentId, state);
    return result;
  } catch (error) {
    safeConsole.error('MCP state sync error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown MCP state sync error'
    };
  }
});

ipcMain.handle('mcp:testConnection', async (event, serverId: string) => {
  if (!mcpService) {
    return {
      success: false,
      error: 'MCP service not initialized'
    };
  }

  try {
    const result = await mcpService.testConnection(serverId);
    return result;
  } catch (error) {
    safeConsole.error('MCP test error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown MCP test error'
    };
  }
});

ipcMain.handle('mcp:disconnectServer', async (event, serverId: string) => {
  if (!mcpService) {
    return {
      success: false,
      error: 'MCP service not initialized'
    };
  }

  try {
    const result = await mcpService.disconnectServer(serverId);
    return result;
  } catch (error) {
    safeConsole.error('MCP disconnect error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown MCP disconnect error'
    };
  }
});

ipcMain.handle('mcp:getConnectedServers', async (event) => {
  if (!mcpService) {
    return [];
  }

  try {
    return mcpService.getConnectedServers();
  } catch (error) {
    safeConsole.error('MCP get servers error:', error);
    return [];
  }
});

// ✅ Terminal PTY IPC Handlers
ipcMain.handle('terminal:create', (event, terminalSettings?: {
  shell?: string;
  cols?: number;
  rows?: number;
}) => {
  try {
    // Use settings or fallback to defaults
    const shell = terminalSettings?.shell || (os.platform() === 'win32' ? 'powershell.exe' : 'bash');
    const cols = terminalSettings?.cols || 80;
    const rows = terminalSettings?.rows || 30;

    // ✅ Enhanced environment setup for proper terminal experience
    const enhancedEnv = {
      ...process.env,
      TERM: 'xterm-256color',
      COLORTERM: 'truecolor',
      // ✅ Set up proper bash prompt with current directory
      PS1: '\\[\\033[01;32m\\]\\u@\\h\\[\\033[00m\\]:\\[\\033[01;34m\\]\\w\\[\\033[00m\\]\\$ ',
      // ✅ Enable command history
      HISTSIZE: '1000',
      HISTFILESIZE: '2000',
      // ✅ Set proper locale
      LANG: process.env.LANG || 'en_US.UTF-8',
      LC_ALL: process.env.LC_ALL || 'en_US.UTF-8',
    };

    // ✅ Shell-specific arguments for better experience
    let shellArgs: string[] = [];
    if (shell.includes('bash')) {
      shellArgs = ['--login', '-i']; // Interactive login shell
    } else if (shell.includes('zsh')) {
      shellArgs = ['--login', '-i'];
    }

    const term = spawn(shell, shellArgs, {
      name: 'xterm-256color',
      cols,
      rows,
      cwd: process.env.HOME || process.cwd(),
      env: enhancedEnv,
      encoding: 'utf8',
    });

    const id = `terminal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    terminals[id] = term;

    safeConsole.log(`✅ Terminal created with ID: ${id}, shell: ${shell}, size: ${cols}x${rows}`);
    return id;
  } catch (error) {
    safeConsole.error('❌ Error creating terminal:', error);
    throw error;
  }
});

ipcMain.on('terminal:input', (event, { id, data }) => {
  try {
    const terminal = terminals[id];
    if (!terminal) {
      safeConsole.error(`❌ Terminal not found: ${id}`);
      return;
    }

    // ✅ Basic Security: Block dangerous commands
    const inputStr = data.toString().toLowerCase().trim();
    const forbiddenCommands = [
      'rm -rf /',
      'rm -rf ~',
      'rm -rf *',
      'sudo rm -rf',
      'shutdown',
      'reboot',
      'halt',
      'poweroff',
      'init 0',
      'init 6',
      'mkfs',
      'fdisk',
      'dd if=',
      'format',
      'del /s',
      'rmdir /s',
      'deltree',
    ];

    // Check for forbidden patterns
    const isDangerous = forbiddenCommands.some(cmd =>
      inputStr.includes(cmd) || inputStr.startsWith(cmd)
    );

    if (isDangerous) {
      safeConsole.log(`🔒 Blocked dangerous command: ${inputStr}`);
      // Send security warning directly to terminal
      event.sender.send(`terminal:data:${id}`, '\r\n🔒 Command blocked by security policy. Dangerous operations are not allowed.\r\n$ ');
      return;
    }

    // If command is safe, proceed with execution
    terminal.write(data);
  } catch (error) {
    safeConsole.error('❌ Error writing to terminal:', error);
  }
});

ipcMain.handle('terminal:resize', (event, { id, cols, rows }) => {
  try {
    const terminal = terminals[id];
    if (terminal) {
      terminal.resize(cols, rows);
      safeConsole.log(`✅ Terminal ${id} resized to ${cols}x${rows}`);
      return { success: true };
    } else {
      safeConsole.error(`❌ Terminal not found for resize: ${id}`);
      return { success: false, error: 'Terminal not found' };
    }
  } catch (error) {
    safeConsole.error('❌ Error resizing terminal:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.on('terminal:dispose', (event, id) => {
  try {
    const terminal = terminals[id];
    if (terminal) {
      terminal.kill();
      delete terminals[id];
      safeConsole.log(`✅ Terminal disposed: ${id}`);
    } else {
      safeConsole.error(`❌ Terminal not found for disposal: ${id}`);
    }
  } catch (error) {
    safeConsole.error('❌ Error disposing terminal:', error);
  }
});

ipcMain.on('terminal:listen', (event, id) => {
  try {
    const terminal = terminals[id];
    if (!terminal) {
      safeConsole.error(`❌ Terminal not found for listening: ${id}`);
      return;
    }

    terminal.onData((data: string) => {
      event.sender.send(`terminal:data:${id}`, data);
    });

    terminal.onExit((e: { exitCode: number; signal?: number }) => {
      safeConsole.log(`✅ Terminal ${id} exited with code: ${e.exitCode}, signal: ${e.signal}`);
      event.sender.send(`terminal:exit:${id}`, { exitCode: e.exitCode, signal: e.signal });
      delete terminals[id];
    });

    // ✅ Initialize terminal with proper setup commands after a brief delay
    setTimeout(() => {
      const shell = terminal.process || 'bash'; // Get shell from terminal process
      if (shell.includes('bash')) {
        // ✅ Set up bash with proper prompt and features
        terminal.write('export PS1="\\[\\033[01;32m\\]\\u@\\h\\[\\033[00m\\]:\\[\\033[01;34m\\]\\w\\[\\033[00m\\]\\$ "\r');
        terminal.write('export HISTCONTROL=ignoredups:erasedups\r');
        terminal.write('clear\r');
      } else if (shell.includes('zsh')) {
        // ✅ Set up zsh with proper prompt
        terminal.write('export PS1="%F{green}%n@%m%f:%F{blue}%~%f%# "\r');
        terminal.write('clear\r');
      }
    }, 200);

    safeConsole.log(`✅ Terminal listener attached: ${id}`);
  } catch (error) {
    safeConsole.error('❌ Error setting up terminal listener:', error);
  }
});

// ✅ Task 99: Enhanced Agent Terminal Integration
ipcMain.handle('terminal:agent-command', async (event, { command, agentId, sessionId, timeout, workingDirectory, environment }) => {
  try {
    if (!command || typeof command !== 'string') {
      return {
        success: false,
        error: 'Invalid command: command must be a non-empty string'
      };
    }

    if (!agentId || typeof agentId !== 'string') {
      return {
        success: false,
        error: 'Invalid agentId: agentId must be a non-empty string'
      };
    }

    safeConsole.log(`🤖 Agent ${agentId} requesting shell command: ${command}${sessionId ? ` (session: ${sessionId})` : ''}`);

    // ✅ Task 99: Enhanced session support
    const effectiveTimeout = timeout || 30000; // 30 seconds default
    const effectiveWorkingDir = workingDirectory || process.cwd();
    const effectiveEnvironment = { ...process.env, ...environment };

    // ✅ Security: Block dangerous commands
    const commandLower = command.toLowerCase().trim();
    const disallowedCommands = [
      'rm -rf',
      'sudo rm',
      'sudo',
      'reboot',
      'shutdown',
      'halt',
      'poweroff',
      'init 0',
      'init 6',
      'mkfs',
      'fdisk',
      'dd if=',
      'format',
      'del /s',
      'rmdir /s',
      'deltree',
      'chmod 777',
      'chown',
      'passwd',
      'su -',
      'killall',
      'pkill -9',
    ];

    const isDangerous = disallowedCommands.some(danger =>
      commandLower.includes(danger) || commandLower.startsWith(danger)
    );

    if (isDangerous) {
      const errorMsg = `Blocked by security policy: ${command}`;
      safeConsole.log(`🔒 Agent ${agentId} command blocked: ${errorMsg}`);
      return {
        success: false,
        error: errorMsg
      };
    }

    // ✅ Task 99: Create isolated PTY process with enhanced configuration
    const shell = process.env.SHELL || (process.platform === 'win32' ? 'powershell.exe' : 'bash');

    // ✅ Enhanced environment for agent commands
    const enhancedAgentEnv = {
      ...effectiveEnvironment,
      TERM: 'xterm-256color',
      COLORTERM: 'truecolor',
      PS1: '\\[\\033[01;32m\\]\\u@\\h\\[\\033[00m\\]:\\[\\033[01;34m\\]\\w\\[\\033[00m\\]\\$ ',
      HISTSIZE: '1000',
      HISTFILESIZE: '2000',
      LANG: process.env.LANG || 'en_US.UTF-8',
      LC_ALL: process.env.LC_ALL || 'en_US.UTF-8',
    };

    // ✅ Shell-specific arguments
    let shellArgs: string[] = [];
    if (shell.includes('bash')) {
      shellArgs = ['--login', '-i'];
    } else if (shell.includes('zsh')) {
      shellArgs = ['--login', '-i'];
    }

    const ptyProcess = spawn(shell, shellArgs, {
      name: 'xterm-256color',
      cols: 80,
      rows: 30,
      cwd: effectiveWorkingDir,
      env: enhancedAgentEnv,
      encoding: 'utf8',
    });

    // ✅ Task 99: Store session reference if provided
    if (sessionId && !terminals[sessionId]) {
      terminals[sessionId] = ptyProcess;
      safeConsole.log(`✅ Agent terminal session ${sessionId} created for ${agentId}`);
    }

    return new Promise((resolve) => {
      let output = '';
      let hasResolved = false;

      // Collect output data
      ptyProcess.onData((data: string) => {
        output += data;

        // ✅ Task 100 Step 2: Broadcast terminal log to UI
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send('terminal:log', {
            id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            timestamp: Date.now(),
            agentId,
            command,
            output: data,
            type: 'output',
            sessionId: sessionId || 'isolated',
            success: undefined // Will be determined on completion
          });
        }
      });

      // Handle process exit
      ptyProcess.onExit((e: { exitCode: number; signal?: number }) => {
        if (!hasResolved) {
          hasResolved = true;
          safeConsole.log(`✅ Agent ${agentId} command completed with exit code: ${e.exitCode}`);

          // ✅ Task 100 Step 2: Broadcast command completion to UI
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('terminal:log', {
              id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              timestamp: Date.now(),
              agentId,
              command,
              output: `Command completed with exit code: ${e.exitCode}`,
              type: e.exitCode === 0 ? 'system' : 'error',
              sessionId: sessionId || 'isolated',
              success: e.exitCode === 0
            });
          }

          resolve({
            success: e.exitCode === 0,
            output: output.trim(),
            exitCode: e.exitCode,
            sessionId: sessionId || 'isolated'
          });
        }
      });

      // ✅ Task 100 Step 2: Broadcast command start to UI
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('terminal:log', {
          id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          timestamp: Date.now(),
          agentId,
          command,
          output: `Executing command: ${command}`,
          type: 'command',
          sessionId: sessionId || 'isolated',
          success: undefined
        });
      }

      // Send command to PTY
      ptyProcess.write(`${command}\r`);

      // ✅ Task 99: Configurable timeout
      setTimeout(() => {
        if (!hasResolved) {
          hasResolved = true;
          ptyProcess.kill();
          safeConsole.log(`⏰ Agent ${agentId} command timed out after ${effectiveTimeout}ms: ${command}`);

          // ✅ Task 100 Step 2: Broadcast timeout error to UI
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('terminal:log', {
              id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              timestamp: Date.now(),
              agentId,
              command,
              output: `Command timed out after ${effectiveTimeout}ms`,
              type: 'error',
              sessionId: sessionId || 'isolated',
              success: false
            });
          }

          resolve({
            success: false,
            output: output.trim(),
            error: `Command timed out after ${effectiveTimeout}ms`,
            sessionId: sessionId || 'isolated'
          });
        }
      }, effectiveTimeout);
    });

  } catch (error) {
    safeConsole.error(`❌ Agent shell command error:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
});

// ✅ Task 101 Step 2: TerminalSessionManager IPC Handlers
ipcMain.handle('terminal:create-session', async (event, sessionId: string, agentId: string, options?: {
  shell?: string;
  workingDirectory?: string;
  environment?: Record<string, string>;
  cols?: number;
  rows?: number;
}) => {
  try {
    const ptyProcess = terminalSessionManager.createSession(sessionId, agentId, options);

    // Set up data listener for real-time output
    ptyProcess.onData((data: string) => {
      // Send to Terminal Logs Panel
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('terminal:log', {
          id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          timestamp: Date.now(),
          agentId,
          output: data,
          type: 'output',
          sessionId,
          success: undefined
        });
      }

      // Send to session-specific listeners
      event.sender.send(`terminal:session-data:${sessionId}`, data);
    });

    // Set up exit listener
    ptyProcess.onExit((exitInfo) => {
      event.sender.send(`terminal:session-exit:${sessionId}`, exitInfo);
      safeConsole.log(`✅ TerminalSessionManager: Session ${sessionId} exited`);
    });

    safeConsole.log(`✅ TerminalSessionManager: Created session ${sessionId} for agent ${agentId}`);
    return { success: true, sessionId };
  } catch (error) {
    safeConsole.error(`❌ TerminalSessionManager: Error creating session ${sessionId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create session'
    };
  }
});

ipcMain.handle('terminal:write-session', async (event, sessionId: string, data: string) => {
  try {
    const success = terminalSessionManager.writeToSession(sessionId, data);
    if (!success) {
      return { success: false, error: `Session ${sessionId} not found` };
    }
    return { success: true };
  } catch (error) {
    safeConsole.error(`❌ TerminalSessionManager: Error writing to session ${sessionId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to write to session'
    };
  }
});

ipcMain.handle('terminal:destroy-session', async (event, sessionId: string) => {
  try {
    const success = terminalSessionManager.destroySession(sessionId);
    return { success, sessionId };
  } catch (error) {
    safeConsole.error(`❌ TerminalSessionManager: Error destroying session ${sessionId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to destroy session'
    };
  }
});

ipcMain.handle('terminal:list-sessions', async (event, agentId?: string) => {
  try {
    const sessions = agentId
      ? terminalSessionManager.getSessionsByAgent(agentId)
      : terminalSessionManager.listSessions();
    return { success: true, sessions };
  } catch (error) {
    safeConsole.error(`❌ TerminalSessionManager: Error listing sessions:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to list sessions'
    };
  }
});

ipcMain.handle('terminal:get-session-info', async (event, sessionId: string) => {
  try {
    const sessionInfo = terminalSessionManager.getSessionInfo(sessionId);
    if (!sessionInfo) {
      return { success: false, error: `Session ${sessionId} not found` };
    }
    return { success: true, sessionInfo };
  } catch (error) {
    safeConsole.error(`❌ TerminalSessionManager: Error getting session info ${sessionId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get session info'
    };
  }
});

ipcMain.handle('terminal:resize-session', async (event, sessionId: string, cols: number, rows: number) => {
  try {
    const success = terminalSessionManager.resizeSession(sessionId, cols, rows);
    if (!success) {
      return { success: false, error: `Session ${sessionId} not found` };
    }
    return { success: true };
  } catch (error) {
    safeConsole.error(`❌ TerminalSessionManager: Error resizing session ${sessionId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to resize session'
    };
  }
});

ipcMain.handle('terminal:get-session-stats', async (event) => {
  try {
    const stats = terminalSessionManager.getStats();
    return { success: true, stats };
  } catch (error) {
    safeConsole.error(`❌ TerminalSessionManager: Error getting stats:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get session stats'
    };
  }
});

ipcMain.handle('terminal:cleanup-agent-sessions', async (event, agentId: string) => {
  try {
    const destroyedCount = terminalSessionManager.destroyAgentSessions(agentId);
    return { success: true, destroyedCount };
  } catch (error) {
    safeConsole.error(`❌ TerminalSessionManager: Error cleaning up agent sessions for ${agentId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to cleanup agent sessions'
    };
  }
});
// ✅ Task 104 Step 1: Multi-Session Terminal Support for Users
// User terminal sessions (separate from agent sessions)
const userTerminalSessions = new Map<string, { ptyProcess: IPty; shell: string; createdAt: number }>();

ipcMain.handle('terminal:create-user-session', (event, shell = '/bin/bash') => {
  try {
    const sessionId = crypto.randomUUID();
    const effectiveShell = shell || (os.platform() === 'win32' ? 'powershell.exe' : 'bash');

    // ✅ Enhanced environment for user terminal sessions
    const enhancedEnv = {
      ...process.env,
      TERM: 'xterm-256color',
      COLORTERM: 'truecolor',
      // ✅ Set up proper bash prompt with current directory and colors
      PS1: '\\[\\033[01;32m\\]\\u@\\h\\[\\033[00m\\]:\\[\\033[01;34m\\]\\w\\[\\033[00m\\]\\$ ',
      // ✅ Enable command history and completion
      HISTSIZE: '1000',
      HISTFILESIZE: '2000',
      HISTCONTROL: 'ignoredups:erasedups',
      // ✅ Set proper locale
      LANG: process.env.LANG || 'en_US.UTF-8',
      LC_ALL: process.env.LC_ALL || 'en_US.UTF-8',
    };

    // ✅ Shell-specific arguments for interactive experience
    let shellArgs: string[] = [];
    if (effectiveShell.includes('bash')) {
      shellArgs = ['--login', '-i'];
    } else if (effectiveShell.includes('zsh')) {
      shellArgs = ['--login', '-i'];
    }

    const ptyProcess = spawn(effectiveShell, shellArgs, {
      name: 'xterm-256color',
      cols: 80,
      rows: 24,
      cwd: process.env.HOME || process.cwd(),
      env: enhancedEnv,
      encoding: 'utf8',
    });

    userTerminalSessions.set(sessionId, {
      ptyProcess,
      shell: effectiveShell,
      createdAt: Date.now()
    });

    // Set up data listener
    ptyProcess.onData((data: string) => {
      event.sender.send(`terminal:user-session-data:${sessionId}`, data);
    });

    // Set up exit listener
    ptyProcess.onExit((exitInfo) => {
      userTerminalSessions.delete(sessionId);
      event.sender.send(`terminal:user-session-exit:${sessionId}`, exitInfo);
      safeConsole.log(`✅ User terminal session ${sessionId} exited`);
    });

    // ✅ Initialize terminal with proper setup commands after a brief delay
    setTimeout(() => {
      if (effectiveShell.includes('bash')) {
        // ✅ Set up bash with proper prompt and features
        ptyProcess.write('export PS1="\\[\\033[01;32m\\]\\u@\\h\\[\\033[00m\\]:\\[\\033[01;34m\\]\\w\\[\\033[00m\\]\\$ "\r');
        ptyProcess.write('export HISTCONTROL=ignoredups:erasedups\r');
        ptyProcess.write('clear\r');
      } else if (effectiveShell.includes('zsh')) {
        // ✅ Set up zsh with proper prompt
        ptyProcess.write('export PS1="%F{green}%n@%m%f:%F{blue}%~%f%# "\r');
        ptyProcess.write('clear\r');
      }
    }, 200);

    safeConsole.log(`✅ Created user terminal session ${sessionId} with shell: ${effectiveShell}`);
    return { success: true, sessionId };
  } catch (error) {
    safeConsole.error('❌ Error creating user terminal session:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create session'
    };
  }
});

ipcMain.handle('terminal:dispose-user-session', (event, sessionId: string) => {
  try {
    const session = userTerminalSessions.get(sessionId);
    if (session) {
      session.ptyProcess.kill();
      userTerminalSessions.delete(sessionId);
      safeConsole.log(`✅ Disposed user terminal session ${sessionId}`);
      return { success: true };
    } else {
      return { success: false, error: 'Session not found' };
    }
  } catch (error) {
    safeConsole.error(`❌ Error disposing user terminal session ${sessionId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to dispose session'
    };
  }
});

ipcMain.handle('terminal:write-user-session', (event, sessionId: string, data: string) => {
  try {
    const session = userTerminalSessions.get(sessionId);
    if (session) {
      session.ptyProcess.write(data);
      return { success: true };
    } else {
      return { success: false, error: 'Session not found' };
    }
  } catch (error) {
    safeConsole.error(`❌ Error writing to user terminal session ${sessionId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to write to session'
    };
  }
});

ipcMain.handle('terminal:resize-user-session', (event, sessionId: string, cols: number, rows: number) => {
  try {
    const session = userTerminalSessions.get(sessionId);
    if (session) {
      session.ptyProcess.resize(cols, rows);
      return { success: true };
    } else {
      return { success: false, error: 'Session not found' };
    }
  } catch (error) {
    safeConsole.error(`❌ Error resizing user terminal session ${sessionId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to resize session'
    };
  }
});

ipcMain.handle('terminal:list-user-sessions', (event) => {
  try {
    const sessions = Array.from(userTerminalSessions.entries()).map(([sessionId, session]) => ({
      sessionId,
      shell: session.shell,
      createdAt: session.createdAt
    }));
    return { success: true, sessions };
  } catch (error) {
    safeConsole.error('❌ Error listing user terminal sessions:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to list sessions'
    };
  }
});









app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow();
  }
});

// Cleanup MCP connections and terminals on app quit
app.on('before-quit', async () => {
  if (mcpService) {
    await mcpService.disconnectAll();
  }

  // Cleanup all terminal sessions
  Object.keys(terminals).forEach(id => {
    try {
      terminals[id].kill();
      delete terminals[id];
      safeConsole.log(`✅ Terminal ${id} cleaned up on app quit`);
    } catch (error) {
      safeConsole.error(`❌ Error cleaning up terminal ${id}:`, error);
    }
  });

  // ✅ Task 101 Step 2: Cleanup TerminalSessionManager sessions
  try {
    const destroyedCount = terminalSessionManager.destroyAllSessions();
    safeConsole.log(`✅ TerminalSessionManager: Cleaned up ${destroyedCount} sessions on app quit`);
  } catch (error) {
    safeConsole.error(`❌ TerminalSessionManager: Error during cleanup:`, error);
  }

  // ✅ Task 104: Cleanup user terminal sessions
  userTerminalSessions.forEach((session, sessionId) => {
    try {
      session.ptyProcess.kill();
      safeConsole.log(`✅ User terminal session ${sessionId} cleaned up on app quit`);
    } catch (error) {
      safeConsole.error(`❌ Error cleaning up user terminal session ${sessionId}:`, error);
    }
  });
  userTerminalSessions.clear();
});