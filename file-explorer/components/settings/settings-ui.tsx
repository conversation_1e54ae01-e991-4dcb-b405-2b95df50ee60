// components/settings/settings-ui.tsx
import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { SettingsManager, AllSettings, AgentSettings, TerminalSettings, TaskmasterSettings } from './settings-manager';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Toolt<PERSON>, TooltipContent, TooltipTrigger, TooltipProvider } from '@/components/ui/tooltip';
import { Save, Download, Upload, RotateCcw, Eye, EyeOff, Info, AlertTriangle, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { ApiKeysSettings } from './api-keys-settings';
import { getAllProviders, getProviderModels } from '../agents/llm-provider-registry';
import { IsolatedAgentCard } from './isolated-agent-card';
import { IsolatedSystemTab } from './isolated-system-tab';
import { IsolatedCostTab } from './isolated-cost-tab';
import { IsolatedPrivacyTab } from './isolated-privacy-tab';
import { IsolatedEditorTab } from './isolated-editor-tab';
import { IsolatedTerminalTab } from './isolated-terminal-tab';
import { IsolatedTaskmasterTab } from './isolated-taskmaster-tab';

import { CompleteAgentManager } from '../agents/agent-manager-complete';

/**
 * Temperature compatibility map based on official documentation
 * ✅ = Supports temperature parameter
 * ❌ = Does not support temperature parameter
 */
interface TemperatureConfig {
  supported: boolean;
  models: readonly string[];
  excludedModels?: readonly string[];
  reason: string;
}

const TEMPERATURE_COMPATIBILITY: Record<string, TemperatureConfig> = {
  // ✅ OpenAI - All models support temperature (0.0-2.0)
  openai: {
    supported: true,
    models: ['*'], // All models
    reason: 'OpenAI API supports temperature for all models'
  },

  // ✅ Anthropic - All Claude models support temperature (0.0-1.0)
  anthropic: {
    supported: true,
    models: ['*'], // All models
    reason: 'Anthropic Claude API supports temperature for all models'
  },

  // ✅ OpenRouter - All models support temperature (0.0-2.0)
  openrouter: {
    supported: true,
    models: ['*'], // All models
    reason: 'OpenRouter proxies temperature to underlying providers'
  },

  // ✅ Azure - All OpenAI models support temperature
  azure: {
    supported: true,
    models: ['*'], // All models
    reason: 'Azure OpenAI uses same API as OpenAI'
  },

  // ✅ Google - Gemini models support temperature (0.0-2.0)
  google: {
    supported: true,
    models: ['*'], // All models
    reason: 'Google Gemini API supports temperature parameter'
  },

  // ⚠️ DeepSeek - Most models support temperature, except deepseek-reasoner
  deepseek: {
    supported: true,
    models: ['*'],
    excludedModels: ['deepseek-reasoner'] as readonly string[],
    reason: 'DeepSeek API supports temperature except for reasoning models'
  },

  // ✅ Fireworks - All models support temperature
  fireworks: {
    supported: true,
    models: ['*'], // All models
    reason: 'Fireworks AI API supports temperature for all models'
  }
} as const;

/**
 * Check if a model supports temperature parameter
 */
function modelSupportsTemperature(provider: string, modelId: string): boolean {
  const config = TEMPERATURE_COMPATIBILITY[provider];

  if (!config) {
    // Unknown provider - default to not supported for safety
    return false;
  }

  if (!config.supported) {
    return false;
  }

  // Check if model is specifically excluded
  if (config.excludedModels && config.excludedModels.some(excluded =>
    modelId.toLowerCase().includes(excluded.toLowerCase())
  )) {
    return false;
  }

  return true;
}

/**
 * Get temperature support info for display
 */
function getTemperatureSupportInfo(provider: string, modelId: string): {
  supported: boolean;
  reason: string;
} {
  const config = TEMPERATURE_COMPATIBILITY[provider];

  if (!config) {
    return {
      supported: false,
      reason: 'Temperature support unknown for this provider'
    };
  }

  if (!config.supported) {
    return {
      supported: false,
      reason: config.reason || 'Provider does not support temperature'
    };
  }

  // Check if model is specifically excluded
  if (config.excludedModels && config.excludedModels.some(excluded =>
    modelId.toLowerCase().includes(excluded.toLowerCase())
  )) {
    return {
      supported: false,
      reason: 'This specific model does not support temperature tuning'
    };
  }

  return {
    supported: true,
    reason: config.reason || 'Model supports temperature parameter'
  };
}

/**
 * Debounce utility for batching rapid updates
 */
function useDebounce<T extends (...args: any[]) => void>(
  callback: T,
  delay: number
): T {
  const timeoutRef = useRef<NodeJS.Timeout>();

  return useCallback((...args: Parameters<T>) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]) as T;
}

/**
 * Performance timing utility for measuring input lag
 */
function usePerformanceTimer(name: string) {
  return useCallback((action: string) => {
    console.time(`${name}-${action}`);
    return () => console.timeEnd(`${name}-${action}`);
  }, [name]);
}

interface SettingsUIProps {
  settingsManager: SettingsManager;
  agentManager?: CompleteAgentManager;
  onClose: () => void;
}

export const SettingsUI: React.FC<SettingsUIProps> = ({ settingsManager, agentManager, onClose }) => {
  const [settings, setSettings] = useState<AllSettings>(settingsManager.getSettings());
  const [showApiKeys, setShowApiKeys] = useState(false);
  const [activeTab, setActiveTab] = useState('system');

  // Performance monitoring
  const timer = usePerformanceTimer('AgentSettings');

  useEffect(() => {
    const handleSettingsChange = (newSettings: AllSettings) => {
      setSettings(newSettings);
    };

    settingsManager.onSettingsChange(handleSettingsChange);
    return () => settingsManager.offSettingsChange(handleSettingsChange);
  }, [settingsManager]);

  const handleSystemSettingChange = (key: keyof typeof settings.system, value: any) => {
    settingsManager.updateSystemSettings({ [key]: value });
  };

  // ✅ Principle: Remove Global Re-render Triggers
  // Single stable update function for all agent changes
  const updateAgent = useCallback((agentId: string, updates: Partial<AgentSettings>) => {
    const endTimer = timer('agent-update');
    settingsManager.updateAgentSettings(agentId, updates);
    endTimer();
  }, [settingsManager, timer]);

  // ✅ Stable update functions for each tab (memoized to prevent re-renders)
  const updateSystemSettings = useCallback((updates: Partial<typeof settings.system>) => {
    const endTimer = timer('system-update');
    settingsManager.updateSystemSettings(updates);
    endTimer();
  }, [settingsManager, timer]);

  const updateCostSettings = useCallback((updates: Partial<typeof settings.cost>) => {
    const endTimer = timer('cost-update');
    settingsManager.updateCostSettings(updates);
    endTimer();
  }, [settingsManager, timer]);

  const updatePrivacySettings = useCallback((updates: Partial<typeof settings.privacy>) => {
    const endTimer = timer('privacy-update');
    settingsManager.updatePrivacySettings(updates);
    endTimer();
  }, [settingsManager, timer]);

  const updateEditorSettings = useCallback((updates: Partial<typeof settings.editor>) => {
    const endTimer = timer('editor-update');
    settingsManager.updateEditorSettings(updates);
    endTimer();
  }, [settingsManager, timer]);

  const updateTerminalSettings = useCallback((updates: Partial<typeof settings.terminal>) => {
    const endTimer = timer('terminal-update');
    settingsManager.updateTerminalSettings(updates);
    endTimer();
  }, [settingsManager, timer]);

  const updateTaskmasterSettings = useCallback((updates: Partial<typeof settings.taskmaster>) => {
    const endTimer = timer('taskmaster-update');
    settingsManager.updateTaskmasterSettings(updates);
    endTimer();
  }, [settingsManager, timer]);

  // Stable provider data (memoized to prevent re-renders)
  const providerData = useMemo(() => ({
    providers: getAllProviders(),
    getModelsForProvider: (provider: string) => getProviderModels(provider)
  }), []);

  const handleApiKeyChange = (provider: string, key: string) => {
    if (key.trim()) {
      settingsManager.setApiKey(provider, key);
    } else {
      settingsManager.removeApiKey(provider);
    }
  };

  const { toast } = useToast();

  const exportSettings = () => {
    try {
      const blob = settingsManager.exportSettingsAsBlob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `synapse-settings-${new Date().toISOString().split('T')[0]}.json`;
      a.click();
      URL.revokeObjectURL(url);

      toast({
        title: "Settings Exported",
        description: "Your settings have been successfully exported to a JSON file.",
        variant: "default",
        duration: 3000,
      });
    } catch (error) {
      console.error('Export failed:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export settings. Please try again.",
        variant: "destructive",
        duration: 5000,
      });
    }
  };

  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;

      try {
        // Use the new validation system
        const { importSettings, validateSettingsSchema } = require('../../lib/io/settings-exporter');
        const result = importSettings(content);

        if (!result.success) {
          toast({
            title: "Import Failed",
            description: result.error || "Invalid settings file format.",
            variant: "destructive",
            duration: 5000,
          });
          return;
        }

        // Show warnings if any
        if (result.warnings && result.warnings.length > 0) {
          toast({
            title: "Import Warnings",
            description: result.warnings.join(', '),
            variant: "default",
            duration: 5000,
          });
        }

        // Confirm before applying
        const confirmMessage = `Are you sure you want to import these settings? This will overwrite your current configuration (except API keys).`;

        if (window.confirm(confirmMessage)) {
          if (result.settings) {
            settingsManager.applySettingsSnapshot(result.settings);

            toast({
              title: "Settings Imported",
              description: "Your settings have been successfully imported and applied.",
              variant: "default",
              duration: 3000,
            });

            // Refresh the UI by triggering a re-render
            window.location.reload();
          }
        }
      } catch (error) {
        console.error('Import failed:', error);
        toast({
          title: "Import Failed",
          description: "Failed to process the settings file. Please check the file format.",
          variant: "destructive",
          duration: 5000,
        });
      }
    };

    reader.onerror = () => {
      toast({
        title: "File Read Error",
        description: "Failed to read the selected file. Please try again.",
        variant: "destructive",
        duration: 5000,
      });
    };

    reader.readAsText(file);

    // Reset the input so the same file can be selected again
    event.target.value = '';
  };



  return (
    <TooltipProvider>
      <div className="h-full flex flex-col">
        <div className="border-b border-border p-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Settings</h2>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={exportSettings}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <label htmlFor="import-settings" className="cursor-pointer">
              <Button variant="outline" size="sm" asChild>
                <span>
                  <Upload className="h-4 w-4 mr-2" />
                  Import
                </span>
              </Button>
              <input
                id="import-settings"
                type="file"
                accept=".json"
                className="hidden"
                onChange={importSettings}
              />
            </label>
            <Button variant="outline" size="sm" onClick={() => settingsManager.resetToDefaults()}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            <Button onClick={onClose}>
              <Save className="h-4 w-4 mr-2" />
              Close
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-auto">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          <TabsList className="grid w-full grid-cols-8">
            <TabsTrigger value="system">System</TabsTrigger>
            <TabsTrigger value="agents">Agents</TabsTrigger>
            <TabsTrigger value="api-keys">API Keys</TabsTrigger>
            <TabsTrigger value="cost">Cost</TabsTrigger>
            <TabsTrigger value="privacy">Privacy</TabsTrigger>
            <TabsTrigger value="editor">Editor</TabsTrigger>
            <TabsTrigger value="terminal">Terminal</TabsTrigger>
            <TabsTrigger value="taskmaster">🧠 Taskmaster</TabsTrigger>
          </TabsList>

          {/* ✅ Conditional mounting - only active tab is mounted */}
          {activeTab === 'system' && (
            <IsolatedSystemTab
              settings={settings.system}
              updateSystemSettings={updateSystemSettings}
            />
          )}

          {activeTab === 'agents' && (
            <div className="space-y-6 p-6">
              <div className="grid gap-4">
                {settings.agents.map((agent) => (
                  <IsolatedAgentCard
                    key={agent.id}
                    agent={agent}
                    providers={providerData.providers}
                    getModelsForProvider={providerData.getModelsForProvider}
                    updateAgent={updateAgent}
                  />
                ))}
              </div>
            </div>
          )}

          {activeTab === 'api-keys' && (
            <div className="space-y-6 p-6">
              <ApiKeysSettings settingsManager={settingsManager} settings={settings} />
            </div>
          )}

          {activeTab === 'cost' && (
            <IsolatedCostTab
              settings={settings.cost}
              updateCostSettings={updateCostSettings}
            />
          )}

          {activeTab === 'privacy' && (
            <IsolatedPrivacyTab
              settings={settings.privacy}
              updatePrivacySettings={updatePrivacySettings}
            />
          )}

          {activeTab === 'editor' && (
            <IsolatedEditorTab
              settings={settings.editor}
              updateEditorSettings={updateEditorSettings}
            />
          )}

          {activeTab === 'terminal' && (
            <IsolatedTerminalTab
              settings={settings.terminal}
              updateTerminalSettings={updateTerminalSettings}
            />
          )}

          {activeTab === 'taskmaster' && (
            <IsolatedTaskmasterTab
              settings={settings.taskmaster}
              updateTaskmasterSettings={updateTaskmasterSettings}
            />
          )}



        </Tabs>
      </div>
    </div>
    </TooltipProvider>
  );
};

export default SettingsUI;