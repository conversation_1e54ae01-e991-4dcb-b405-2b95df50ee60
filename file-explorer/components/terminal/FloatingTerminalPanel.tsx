"use client"

import React, { useState, useEffect, useCallback } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Plus, X, Terminal as TerminalIcon } from 'lucide-react';
import TerminalPanel from './TerminalPanel';

interface TerminalSession {
  id: string;
  name: string;
  shell: string;
  createdAt: number;
}

// ✅ Global terminal session state for cross-window synchronization
interface TerminalSyncState {
  sessions: TerminalSession[];
  activeSessionId: string;
  activeShell: string;
}

export default function FloatingTerminalPanel() {
  const [sessions, setSessions] = useState<TerminalSession[]>([
    { id: 'session-1', name: 'Terminal 1', shell: 'bash', createdAt: Date.now() }
  ]);
  const [activeSessionId, setActiveSessionId] = useState<string>('session-1');
  const [activeShell, setActiveShell] = useState<string>('bash');
  const [isInitialized, setIsInitialized] = useState(false);

  // ✅ Cross-window synchronization setup
  useEffect(() => {
    if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
      console.log('🔗 FloatingTerminalPanel: Setting up cross-window synchronization');

      // Listen for terminal state updates from other windows
      const handleTerminalStateUpdate = (syncState: TerminalSyncState) => {
        console.log('🔄 FloatingTerminalPanel: Received terminal state update:', syncState);
        setSessions(syncState.sessions);
        setActiveSessionId(syncState.activeSessionId);
        setActiveShell(syncState.activeShell);
      };

      // Listen for session creation from other windows
      const handleSessionCreated = (session: TerminalSession) => {
        console.log('➕ FloatingTerminalPanel: Session created in another window:', session);
        setSessions(prev => {
          const exists = prev.find(s => s.id === session.id);
          return exists ? prev : [...prev, session];
        });
      };

      // Listen for session closure from other windows
      const handleSessionClosed = (sessionId: string) => {
        console.log('➖ FloatingTerminalPanel: Session closed in another window:', sessionId);
        setSessions(prev => prev.filter(s => s.id !== sessionId));
        if (activeSessionId === sessionId) {
          const remainingSessions = sessions.filter(s => s.id !== sessionId);
          setActiveSessionId(remainingSessions[0]?.id || '');
        }
      };

      // Request initial state from other windows
      const requestInitialState = () => {
        window.electronAPI.ipc.send('request-terminal-state', {
          requestId: `terminal-${Date.now()}`,
          windowId: `floating-${Date.now()}`
        });
      };

      // Set up IPC listeners
      window.electronAPI.ipc.on('terminal-state-update', handleTerminalStateUpdate);
      window.electronAPI.ipc.on('terminal-session-created', handleSessionCreated);
      window.electronAPI.ipc.on('terminal-session-closed', handleSessionClosed);

      // Request initial state if not initialized
      if (!isInitialized) {
        requestInitialState();
        setIsInitialized(true);
      }

      // Cleanup listeners
      return () => {
        window.electronAPI.ipc.removeListener('terminal-state-update', handleTerminalStateUpdate);
        window.electronAPI.ipc.removeListener('terminal-session-created', handleSessionCreated);
        window.electronAPI.ipc.removeListener('terminal-session-closed', handleSessionClosed);
      };
    }
  }, [isInitialized, activeSessionId, sessions]);

  // ✅ Broadcast state changes to other windows
  const broadcastStateUpdate = useCallback((newState: Partial<TerminalSyncState>) => {
    if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
      const fullState: TerminalSyncState = {
        sessions,
        activeSessionId,
        activeShell,
        ...newState
      };
      window.electronAPI.ipc.send('terminal-state-update', fullState);
      console.log('📡 FloatingTerminalPanel: Broadcasting state update:', fullState);
    }
  }, [sessions, activeSessionId, activeShell]);

  const createNewSession = () => {
    const newSession: TerminalSession = {
      id: `session-${Date.now()}`,
      name: `Terminal ${sessions.length + 1}`,
      shell: activeShell,
      createdAt: Date.now()
    };

    const newSessions = [...sessions, newSession];
    setSessions(newSessions);
    setActiveSessionId(newSession.id);

    // ✅ Broadcast session creation to other windows
    if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
      window.electronAPI.ipc.send('terminal-session-created', newSession);
    }

    broadcastStateUpdate({
      sessions: newSessions,
      activeSessionId: newSession.id
    });
  };

  const closeSession = (sessionId: string) => {
    if (sessions.length <= 1) return; // Keep at least one session

    const newSessions = sessions.filter(s => s.id !== sessionId);
    setSessions(newSessions);

    const newActiveId = activeSessionId === sessionId ? (newSessions[0]?.id || '') : activeSessionId;
    setActiveSessionId(newActiveId);

    // ✅ Broadcast session closure to other windows
    if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
      window.electronAPI.ipc.send('terminal-session-closed', sessionId);
    }

    broadcastStateUpdate({
      sessions: newSessions,
      activeSessionId: newActiveId
    });
  };

  const switchToSession = (sessionId: string) => {
    setActiveSessionId(sessionId);
    broadcastStateUpdate({ activeSessionId: sessionId });
  };

  return (
    <div className="flex flex-col h-full bg-zinc-900">
      {/* ✅ Clean Terminal Header with Shell Selector and Right-Aligned Tabs */}
      <div className="flex items-center justify-between px-3 py-1 bg-zinc-950 border-b border-zinc-800">
        <div className="text-sm font-mono text-zinc-400">TERMINAL</div>

        <div className="flex gap-2 items-center">
          {/* Shell Type Selector */}
          <Select value={activeShell} onValueChange={setActiveShell}>
            <SelectTrigger className="w-20 h-6 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="bash">bash</SelectItem>
              <SelectItem value="zsh">zsh</SelectItem>
              <SelectItem value="sh">sh</SelectItem>
              <SelectItem value="fish">fish</SelectItem>
              <SelectItem value="powershell">pwsh</SelectItem>
            </SelectContent>
          </Select>

          {/* Terminal Session Tabs - Right Aligned */}
          {sessions.map((session) => (
            <div
              key={session.id}
              className={`flex items-center gap-1 px-2 py-1 rounded text-xs cursor-pointer ${
                session.id === activeSessionId
                  ? 'bg-zinc-700 text-white'
                  : 'bg-zinc-800 text-zinc-300 hover:bg-zinc-700'
              }`}
              onClick={() => switchToSession(session.id)}
            >
              <TerminalIcon className="w-3 h-3" />
              <span>{session.name}</span>
              {sessions.length > 1 && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    closeSession(session.id);
                  }}
                  className="ml-1 hover:bg-zinc-600 rounded p-0.5"
                >
                  <X className="w-2 h-2" />
                </button>
              )}
            </div>
          ))}

          {/* New Terminal Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={createNewSession}
            className="h-6 w-6 p-0 text-zinc-400 hover:text-zinc-200"
            title="New Terminal"
          >
            <Plus className="w-3 h-3" />
          </Button>
        </div>
      </div>

      {/* Terminal Content */}
      <div className="flex-1 overflow-hidden">
        <TerminalPanel 
          className="h-full" 
          key={activeSessionId}
          sessionId={activeSessionId}
        />
      </div>
    </div>
  );
}
