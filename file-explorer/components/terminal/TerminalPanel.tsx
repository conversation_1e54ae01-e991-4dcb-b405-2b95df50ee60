"use client"

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, TestTube, Terminal as TerminalIcon, Settings } from 'lucide-react';
import TerminalBootstrap from './TerminalBootstrap';
import { terminalEventBus } from './terminal-event-bus';
import { terminalSessionLoggingService } from '../services/terminal-session-logging-service';



// ✅ Task 94: Agent configuration for terminal routing
interface AgentConfig {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  capabilities: string[];
}

// ✅ Task 97: Terminal session management types
// ✅ Task 98: Extended with command output logging
interface TerminalSession {
  id: string;
  name: string;
  terminal: any; // xterm Terminal instance
  ptyId?: string; // Backend PTY process ID
  isActive: boolean;
  createdAt: number;
  // Session-specific state
  commandHistory: string[];
  historyIndex: number;
  currentInput: string;
  activeAgentId: string;
  isAgentMode: boolean;
  // ✅ Task 98: Session logging
  log: string[]; // Store all input/output logs line by line
  logEnabled: boolean; // Toggle logging per session
}

const AVAILABLE_AGENTS: AgentConfig[] = [
  {
    id: 'micromanager',
    name: 'Micromanager',
    description: 'Task orchestration and decomposition',
    icon: <Settings className="w-4 h-4" />,
    color: 'bg-purple-500',
    capabilities: ['Task Planning', 'Coordination', 'Resource Management']
  },
  {
    id: 'intern',
    name: 'Intern Agent',
    description: 'Basic tasks and learning',
    icon: <User className="w-4 h-4" />,
    color: 'bg-green-500',
    capabilities: ['Simple Tasks', 'Documentation', 'Basic Operations']
  },
  {
    id: 'senior-agent',
    name: 'Senior Agent',
    description: 'Complex development tasks',
    icon: <Bot className="w-4 h-4" />,
    color: 'bg-blue-500',
    capabilities: ['Code Review', 'Architecture', 'Complex Problem Solving']
  },
  {
    id: 'designer',
    name: 'Designer Agent',
    description: 'UI/UX design and prototyping',
    icon: <Wrench className="w-4 h-4" />,
    color: 'bg-pink-500',
    capabilities: ['UI Design', 'Prototyping', 'User Experience']
  },
  {
    id: 'tester',
    name: 'Tester Agent',
    description: 'Testing and quality assurance',
    icon: <TestTube className="w-4 h-4" />,
    color: 'bg-orange-500',
    capabilities: ['Test Automation', 'Quality Assurance', 'Bug Detection']
  }
];

interface TerminalPanelProps {
  className?: string;
  onReady?: (terminal: any) => void;
  defaultAgentId?: string;
}

export default function TerminalPanel({
  className = '',
  onReady,
  defaultAgentId = 'senior-agent'
}: TerminalPanelProps) {
  // ✅ Task 97: Multi-session terminal state
  const [sessions, setSessions] = useState<TerminalSession[]>([]);
  const [activeSessionId, setActiveSessionId] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const terminalContainerRef = useRef<HTMLDivElement>(null);

  // Get current active session
  const activeSession = sessions.find(session => session.id === activeSessionId);

  // Legacy state for backward compatibility (derived from active session)
  const activeAgentId = activeSession?.activeAgentId || defaultAgentId;
  const isAgentMode = activeSession?.isAgentMode || false;
  const terminalInstance = activeSession?.terminal || null;
  const commandHistory = activeSession?.commandHistory || [];
  const historyIndex = activeSession?.historyIndex || -1;
  const currentInput = activeSession?.currentInput || '';
  const inputBufferRef = useRef<string>('');

  // ✅ Cross-window synchronization setup
  useEffect(() => {
    if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
      console.log('🔗 TerminalPanel: Setting up cross-window synchronization');

      // Listen for terminal state updates from other windows
      const handleTerminalStateUpdate = (syncState: any) => {
        console.log('🔄 TerminalPanel: Received terminal state update:', syncState);
        if (syncState.sessions) {
          setSessions(syncState.sessions);
        }
        if (syncState.activeSessionId) {
          setActiveSessionId(syncState.activeSessionId);
        }
      };

      // Listen for session creation from other windows
      const handleSessionCreated = (session: any) => {
        console.log('➕ TerminalPanel: Session created in another window:', session);
        setSessions(prev => {
          const exists = prev.find(s => s.id === session.id);
          return exists ? prev : [...prev, session];
        });
      };

      // Listen for session closure from other windows
      const handleSessionClosed = (sessionId: string) => {
        console.log('➖ TerminalPanel: Session closed in another window:', sessionId);
        setSessions(prev => prev.filter(s => s.id !== sessionId));
        if (activeSessionId === sessionId) {
          const remainingSessions = sessions.filter(s => s.id !== sessionId);
          setActiveSessionId(remainingSessions[0]?.id || null);
        }
      };

      // Request initial state from other windows
      const requestInitialState = () => {
        if (window.electronAPI?.ipc) {
          window.electronAPI.ipc.send('request-terminal-state', {
            requestId: `terminal-${Date.now()}`,
            windowId: `main-${Date.now()}`
          });
        }
      };

      // Set up IPC listeners and get cleanup functions
      const cleanupStateUpdate = window.electronAPI.ipc.on('terminal-state-update', handleTerminalStateUpdate);
      const cleanupSessionCreated = window.electronAPI.ipc.on('terminal-session-created', handleSessionCreated);
      const cleanupSessionClosed = window.electronAPI.ipc.on('terminal-session-closed', handleSessionClosed);

      // Request initial state if not initialized
      if (!isInitialized) {
        requestInitialState();
        setIsInitialized(true);
      }

      // Cleanup listeners
      return () => {
        if (cleanupStateUpdate) cleanupStateUpdate();
        if (cleanupSessionCreated) cleanupSessionCreated();
        if (cleanupSessionClosed) cleanupSessionClosed();
      };
    }
  }, [isInitialized, activeSessionId, sessions]);

  // ✅ Task 96: Known commands for autocomplete
  const KNOWN_COMMANDS = [
    // General commands
    'help', 'clear', 'exit', 'pwd', 'ls', 'cd', 'cat', 'echo', 'whoami', 'date',
    // Agent-specific commands
    'analyze', 'build', 'test', 'run', 'deploy', 'explain', 'summarize',
    'implement', 'create', 'develop', 'design', 'optimize', 'refactor',
    'debug', 'fix', 'integrate', 'validate', 'verify', 'check',
    // File operations
    'file', 'write', 'read', 'modify', 'delete', 'generate',
    // UI/Design commands
    'ui', 'interface', 'component', 'style', 'layout', 'theme',
    // Testing commands
    'spec', 'qa', 'quality', 'coverage', 'automation'
  ];

  const activeAgent = AVAILABLE_AGENTS.find(agent => agent.id === activeAgentId);

  // ✅ Task 97: Session management functions
  const updateSessionState = (sessionId: string, updates: Partial<TerminalSession>) => {
    setSessions(prev => prev.map(session =>
      session.id === sessionId ? { ...session, ...updates } : session
    ));
  };

  const createNewSession = useCallback(async () => {
    const sessionId = `terminal-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const sessionName = `Terminal ${sessions.length + 1}`;

    // Create new session object (terminal will be set when TerminalBootstrap is ready)
    const newSession: TerminalSession = {
      id: sessionId,
      name: sessionName,
      terminal: null,
      ptyId: undefined,
      isActive: true,
      createdAt: Date.now(),
      commandHistory: [],
      historyIndex: -1,
      currentInput: '',
      activeAgentId: defaultAgentId,
      isAgentMode: false,
      // ✅ Task 98: Initialize logging properties
      log: [],
      logEnabled: true // Enable logging by default
    };

    // Deactivate all other sessions
    setSessions(prev => [
      ...prev.map(session => ({ ...session, isActive: false })),
      newSession
    ]);

    setActiveSessionId(sessionId);

    // ✅ Task 98: Initialize session logging
    terminalSessionLoggingService.initializeSessionLog(sessionId, sessionName);

    return sessionId;
  }, [sessions.length, defaultAgentId]);

  const closeSession = useCallback((sessionId: string) => {
    const sessionToClose = sessions.find(s => s.id === sessionId);
    if (sessionToClose?.terminal) {
      sessionToClose.terminal.dispose();
    }

    // ✅ Task 98: Complete session logging before closing
    terminalSessionLoggingService.completeSessionLog(sessionId);

    const remainingSessions = sessions.filter(s => s.id !== sessionId);
    setSessions(remainingSessions);

    if (sessionId === activeSessionId) {
      if (remainingSessions.length > 0) {
        const newActiveSession = remainingSessions[0];
        setActiveSessionId(newActiveSession.id);
        updateSessionState(newActiveSession.id, { isActive: true });
      } else {
        setActiveSessionId(null);
      }
    }
  }, [sessions, activeSessionId]);

  const switchToSession = useCallback((sessionId: string) => {
    // Deactivate current session
    if (activeSessionId) {
      updateSessionState(activeSessionId, { isActive: false });
    }

    // Activate new session
    updateSessionState(sessionId, { isActive: true });
    setActiveSessionId(sessionId);
  }, [activeSessionId]);

  // ✅ Task 98: Session logging utility functions
  const appendToSessionLog = useCallback((sessionId: string, entry: string) => {
    const session = sessions.find(s => s.id === sessionId);
    if (session && session.logEnabled) {
      // Update local session log
      updateSessionState(sessionId, {
        log: [...session.log, entry]
      });

      // Also log to the logging service for export/viewing
      terminalSessionLoggingService.addLogEntry(sessionId, entry, 'output');
    }
  }, [sessions]);

  const logUserInput = useCallback((sessionId: string, input: string) => {
    const session = sessions.find(s => s.id === sessionId);
    if (session && session.logEnabled) {
      const formattedInput = `> ${input}`;
      updateSessionState(sessionId, {
        log: [...session.log, formattedInput]
      });

      terminalSessionLoggingService.logInput(sessionId, input);
    }
  }, [sessions]);

  const toggleSessionLogging = useCallback((sessionId: string) => {
    const session = sessions.find(s => s.id === sessionId);
    if (session) {
      const newLogEnabled = !session.logEnabled;
      updateSessionState(sessionId, { logEnabled: newLogEnabled });

      // Log the toggle action
      const message = newLogEnabled ? 'Logging enabled' : 'Logging disabled';
      terminalSessionLoggingService.addLogEntry(sessionId, `[SYSTEM] ${message}`, 'system');
    }
  }, [sessions]);

  const viewSessionLog = useCallback((sessionId: string) => {
    const sessionLog = terminalSessionLoggingService.getSessionLog(sessionId);
    if (sessionLog) {
      console.log(`📜 Log for ${sessionLog.sessionName}:`, sessionLog.entries);
      // Could also open a modal or new window here
    }
  }, []);

  const exportSessionLog = useCallback((sessionId: string) => {
    const logContent = terminalSessionLoggingService.exportSessionLog(sessionId);
    if (logContent) {
      const session = sessions.find(s => s.id === sessionId);
      const filename = `terminal-log-${session?.name || sessionId}-${new Date().toISOString().slice(0, 10)}.txt`;

      // Create and download file
      const blob = new Blob([logContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  }, [sessions]);

  // ✅ Task 96: Helper functions for input management (updated for sessions)
  const updateTerminalInput = (newInput: string) => {
    if (!terminalInstance || !activeSessionId) return;

    // Clear current line and redraw with new input
    const promptLength = 2; // "$ " length
    const currentLineLength = promptLength + currentInput.length;

    // Move cursor to beginning of input and clear line
    terminalInstance.write('\r' + ' '.repeat(currentLineLength) + '\r$ ' + newInput);

    // Update session state
    updateSessionState(activeSessionId, { currentInput: newInput });
    inputBufferRef.current = newInput;
  };

  const addToHistory = (command: string) => {
    if (!activeSessionId) return;

    if (command.trim() && !commandHistory.includes(command.trim())) {
      const newHistory = [...commandHistory, command.trim()];
      updateSessionState(activeSessionId, {
        commandHistory: newHistory,
        historyIndex: -1
      });
    }
  };

  const navigateHistory = (direction: 'up' | 'down') => {
    if (!activeSessionId) return;

    if (direction === 'up') {
      if (commandHistory.length > 0 && historyIndex < commandHistory.length - 1) {
        const newIndex = historyIndex + 1;
        const historyCommand = commandHistory[commandHistory.length - 1 - newIndex];
        updateSessionState(activeSessionId, { historyIndex: newIndex });
        updateTerminalInput(historyCommand);
      }
    } else {
      if (historyIndex > 0) {
        const newIndex = historyIndex - 1;
        const historyCommand = commandHistory[commandHistory.length - 1 - newIndex];
        updateSessionState(activeSessionId, { historyIndex: newIndex });
        updateTerminalInput(historyCommand);
      } else if (historyIndex === 0) {
        updateSessionState(activeSessionId, { historyIndex: -1 });
        updateTerminalInput('');
      }
    }
  };

  const autocompleteCommand = () => {
    const input = currentInput.trim();
    if (!input) return;

    // Find matching commands
    const matches = KNOWN_COMMANDS.filter(cmd => cmd.startsWith(input.toLowerCase()));

    if (matches.length === 1) {
      // Single match - complete it
      updateTerminalInput(matches[0]);
    } else if (matches.length > 1) {
      // Multiple matches - show them
      terminalInstance?.writeln(`\r\n💡 Available completions: ${matches.join(', ')}`);
      terminalInstance?.write('$ ' + currentInput);
    }
  };

  // ✅ Task 94 Step 1: Handle terminal ready event (updated for sessions)
  const handleTerminalReady = (terminal: any) => {
    // Update the active session with the terminal instance
    if (activeSessionId) {
      updateSessionState(activeSessionId, { terminal });
    }
    onReady?.(terminal);

    // ✅ Task 94 Step 2: Set up input interception for agent routing
    terminal.onData((input: string) => {
      // ✅ Task 98: Log user input if not in agent mode
      if (!isAgentMode && activeSessionId) {
        logUserInput(activeSessionId, input);
      }

      if (isAgentMode) {
        handleAgentInput(input);
      }
      // Note: Normal PTY input is handled by TerminalBootstrap
    });

    // Display initial agent mode status
    if (isAgentMode) {
      displayAgentModeStatus(terminal);
    }
  };

  // ✅ Task 94 Step 2: Handle input routing to agents (Enhanced with Task 96 features)
  const handleAgentInput = (input: string) => {
    if (!activeSessionId) return;

    // ✅ Task 96: Handle special keys for history and autocomplete
    if (input === '\r') {
      // Enter key - process command
      if (currentInput.trim()) {
        // ✅ Task 98: Log agent command input
        logUserInput(activeSessionId, currentInput.trim());

        addToHistory(currentInput.trim());
        processAgentCommand(currentInput.trim());
        updateSessionState(activeSessionId, { currentInput: '' });
        inputBufferRef.current = '';
      }
      terminalInstance?.write('\r\n$ ');
    } else if (input === '\u007F') {
      // Backspace
      if (currentInput.length > 0) {
        const newInput = currentInput.slice(0, -1);
        updateTerminalInput(newInput);
      }
    } else if (input === '\u0003') {
      // Ctrl+C - cancel current input
      updateSessionState(activeSessionId, {
        currentInput: '',
        historyIndex: -1
      });
      inputBufferRef.current = '';
      terminalInstance?.write('^C\r\n$ ');
    } else if (input === '\t') {
      // ✅ Task 96: Tab key - autocomplete
      autocompleteCommand();
    } else if (input === '\u001b[A') {
      // ✅ Task 96: Arrow Up - navigate history up
      navigateHistory('up');
    } else if (input === '\u001b[B') {
      // ✅ Task 96: Arrow Down - navigate history down
      navigateHistory('down');
    } else if (input === '\u001b[C' || input === '\u001b[D') {
      // Arrow Left/Right - ignore for now (could implement cursor movement later)
      return;
    } else if (input.charCodeAt(0) >= 32 && input.charCodeAt(0) <= 126) {
      // Regular printable character
      const newInput = currentInput + input;
      updateTerminalInput(newInput);
    }
  };

  // ✅ Task 94 Step 3: Process agent commands
  const processAgentCommand = async (command: string) => {
    if (!activeAgent || !terminalInstance) return;

    try {
      // Display command being processed
      terminalInstance.writeln(`\r\n🤖 [${activeAgent.name}] Processing: ${command}`);

      // ✅ Task 96: Create enhanced context with command history
      const context = {
        input: command,
        agentId: activeAgentId,
        timestamp: Date.now(),
        // ✅ Task 96: Include command history for agent awareness
        previousCommands: commandHistory.slice(-5), // Last 5 commands
        historyContext: {
          totalCommands: commandHistory.length,
          recentCommands: commandHistory.slice(-3),
          isRepeatedCommand: commandHistory.includes(command)
        }
      };

      // ✅ Task 94 Step 3: Route to agent via message bus or agent manager
      terminalEventBus.emit('terminal:manual-command', context);

      // Show processing indicator with history context
      if (commandHistory.includes(command)) {
        terminalInstance.writeln(`🔄 Repeated command detected - routing to ${activeAgent.name}...`);
      } else {
        terminalInstance.writeln(`⏳ Routing command to ${activeAgent.name}...`);
      }

    } catch (error) {
      terminalInstance.writeln(`❌ Error processing command: ${error instanceof Error ? error.message : String(error)}`);
      terminalInstance.write('$ ');
    }
  };

  // ✅ Display agent mode status
  const displayAgentModeStatus = (terminal: any) => {
    if (!activeAgent) return;

    terminal.writeln('\r\n🤖 Agent Terminal Mode Activated');
    terminal.writeln(`📋 Active Agent: ${activeAgent.name}`);
    terminal.writeln(`📝 Description: ${activeAgent.description}`);
    terminal.writeln(`🔧 Capabilities: ${activeAgent.capabilities.join(', ')}`);
    terminal.writeln('💡 Type commands to route them to the selected agent');
    terminal.writeln('🔄 Use the dropdown above to switch agents');
    terminal.writeln('⚡ Toggle "Agent Mode" to switch between direct terminal and agent routing');
    terminal.write('\r\n$ ');
  };

  // ✅ Handle agent mode toggle (updated for sessions)
  const handleAgentModeToggle = () => {
    if (!activeSessionId) return;

    const newMode = !isAgentMode;
    updateSessionState(activeSessionId, { isAgentMode: newMode });

    if (terminalInstance) {
      if (newMode) {
        terminalInstance.writeln('\r\n🤖 Switched to Agent Mode');
        displayAgentModeStatus(terminalInstance);
      } else {
        terminalInstance.writeln('\r\n💻 Switched to Direct Terminal Mode');
        terminalInstance.writeln('Commands will now be executed directly in the shell');
      }
    }
  };

  // ✅ Handle agent selection change (updated for sessions)
  const handleAgentChange = (newAgentId: string) => {
    if (!activeSessionId) return;

    updateSessionState(activeSessionId, { activeAgentId: newAgentId });
    const newAgent = AVAILABLE_AGENTS.find(agent => agent.id === newAgentId);

    if (terminalInstance && isAgentMode && newAgent) {
      terminalInstance.writeln(`\r\n🔄 Switched to ${newAgent.name}`);
      terminalInstance.writeln(`📝 ${newAgent.description}`);
      terminalInstance.write('$ ');
    }
  };

  // ✅ Task 94 Step 4: Listen for agent responses
  useEffect(() => {
    const unsubscribe = terminalEventBus.on('terminal:agent-output', ({ output, agentId, success, status }) => {
      if (terminalInstance && agentId === activeAgentId) {
        // ✅ Task 95: Handle different response statuses with appropriate colors
        if (status === 'unsupported') {
          // Yellow color for unsupported commands
          terminalInstance.write(`\r\n\x1b[33m🧠 ${activeAgent?.name} does not support this command\x1b[0m\r\n`);
          terminalInstance.writeln(`\x1b[33m${output}\x1b[0m`);
        } else if (status === 'delegated') {
          // Blue color for delegated commands
          terminalInstance.write(`\r\n\x1b[34m🔄 ${activeAgent?.name} delegated command\x1b[0m\r\n`);
          terminalInstance.writeln(`\x1b[34m${output}\x1b[0m`);
        } else if (success === false) {
          // Red color for failed commands
          terminalInstance.write(`\r\n\x1b[31m❌ Error from ${activeAgent?.name}:\x1b[0m\r\n`);
          terminalInstance.writeln(`\x1b[31m${output}\x1b[0m`);
        } else {
          // Green color for successful commands
          terminalInstance.writeln(`\r\n\x1b[32m✅ Response from ${activeAgent?.name}:\x1b[0m`);
          terminalInstance.writeln(output);
        }
        terminalInstance.write('\r\n$ ');
      }
    });

    return unsubscribe;
  }, [terminalInstance, activeAgentId, activeAgent]);

  // ✅ Task 97: Initialize first session on mount
  useEffect(() => {
    if (sessions.length === 0) {
      createNewSession();
    }
  }, [sessions.length, createNewSession]);

  return (
    <div className={`terminal-panel-container flex flex-col h-full ${className}`}>
      {/* ✅ Terminal Content - Fills full space (headers handled by FloatingTerminalPanel) */}
      <div className="flex-1 w-full overflow-hidden bg-black" ref={terminalContainerRef}>
        {activeSessionId ? (
          <TerminalBootstrap
            key={activeSessionId} // Force re-mount when session changes
            className="h-full w-full"
            onReady={handleTerminalReady}
            sessionId={activeSessionId}
            onInput={(input) => {
              // ✅ Task 98: Log input to session logging service
              if (activeSession?.logEnabled) {
                terminalSessionLoggingService.addLogEntry(activeSessionId, input, 'input');
              }
            }}
            onOutput={(output) => {
              // ✅ Task 98: Log output to session logging service
              if (activeSession?.logEnabled) {
                terminalSessionLoggingService.addLogEntry(activeSessionId, output, 'output');
              }
            }}
          />
        ) : (
          <div className="flex items-center justify-center h-full text-zinc-500">
            <div className="text-center">
              <TerminalIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No terminal sessions</p>
              <button
                onClick={createNewSession}
                className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Create New Terminal
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
